@value variables: "@styles/variables.module.css";
@value gray900, colorBlack, colorWhite, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, gray300 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md, breakpoint-lg, breakpoint-xl-1024, breakpoint-xl, breakpoint-xl-2000 from breakpoints;

.embla {
  overflow: hidden;
  background-color: colorBlack;
}

.embla_placeholder {
  position: relative;
  object-fit: cover;
  background-color: colorBlack;
  width: 100%;
  height: 798px;
  display: flex;
  padding: 149px 124px 80px 124px;
  background-size: cover !important;
  background-repeat: no-repeat !important;
  background-position: center;
  overflow: hidden;

  /* Tablet styles (768px - 1024px) */
  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1024) {
    height: 600px;
    padding: 100px 80px 60px 80px;
  }

  /* Desktop styles (1024px - 1200px) */
  @media (min-width: breakpoint-xl-1024) and (max-width: breakpoint-xl) {
    height: 700px;
    padding: 120px 100px 70px 100px;
  }

  /* Large desktop styles (1200px - 2000px) */
  @media (min-width: breakpoint-xl) and (max-width: breakpoint-xl-2000) {
    height: 798px;
    padding: 149px 124px 80px 124px;
  }
}

.inner_container_blur {
  position: absolute;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 103px;
  min-height: 512px;

  .section_top {
    opacity: 1;
  }
}

.embla_resources {
  overflow: hidden;
  background-color: gray300;
}

.embla__viewport {
  width: 100%;
  min-height: 700px;
}

.embla__viewport_resources {
  width: 100%;
}

.embla__container {
  display: flex;
}

.embla__slide_image {
  object-fit: cover;
}

.thumbnail_image {
  box-shadow: none !important;
}

.image_hidden {
  display: none;
  transition: opacity 1.3s ease-in-out;
}

.image_visible {
  display: block;
  transition: opacity 1.3s ease-in-out;
}

.embla__slide {
  flex: 0 0 100%;
  min-width: 0;
  opacity: 0;
  transition: opacity ease-in-out;

  background-size: cover !important;
  background-repeat: no-repeat !important;
  background-position: center;
  padding: 149px 124px 80px 124px;

  /* Tablet styles (768px - 1024px) */
  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1024) {
    padding: 100px 80px 60px 80px;
  }

  /* Desktop styles (1024px - 1200px) */
  @media (min-width: breakpoint-xl-1024) and (max-width: breakpoint-xl) {
    padding: 120px 100px 70px 100px;
  }

  /* Large desktop styles (1200px - 2000px) */
  @media (min-width: breakpoint-xl) and (max-width: breakpoint-xl-2000) {
    padding: 149px 124px 80px 124px;
  }

  @media (min-width: breakpoint-xl-2000) {
    padding: 174px 150px 80px 150px;
  }

  @media (max-width: breakpoint-md) {
    padding-left: 32px;
    padding-right: 32px;
  }
}

.embla__slide_resources {
  flex: 0 0 100%;
  min-width: 0;
  opacity: 0;
  transition: opacity ease-in-out;

  background-size: cover !important;
  background-repeat: no-repeat !important;
  background-position: center;
  padding: 80px 120px;

  /* Tablet styles (768px - 1024px) */
  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1024) {
    padding: 60px 80px;
  }

  /* Desktop styles (1024px - 1200px) */
  @media (min-width: breakpoint-xl-1024) and (max-width: breakpoint-xl) {
    padding: 70px 100px;
  }

  /* Large desktop styles (1200px+) */
  @media (min-width: breakpoint-xl) {
    padding: 80px 120px;
  }

  @media (min-width: breakpoint-xl-2000) {
    padding: 80px 120px;
  }

  @media (max-width: breakpoint-md) {
    padding-left: 32px;
    padding-right: 32px;
  }
}

.embla__slide_resources:active {
  opacity: 1;
}

.embla__slide:active {
  opacity: 1;
}

.embla__slide_mobile {
  flex: 0 0 100%;
  min-width: 0;
}

.embla__slide_mobile img {
  width: 100%;
}

.main_section_mobile {
  display: flex;
  flex-direction: column;
  gap: 25px;
  background-color: colorBlack;
  padding-bottom: 31px;
  min-height: 387px;
}

.main_section_mobile_resources {
  display: flex;
  flex-direction: column;
  gap: 25px;
  background-color: gray300;
  padding-bottom: 31px;
}

.inner_container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 103px;
  min-height: 512px;

  /* Tablet styles (768px - 1024px) */
  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1024) {
    gap: 70px;
    min-height: 400px;
  }

  /* Desktop styles (1024px - 1200px) */
  @media (min-width: breakpoint-xl-1024) and (max-width: breakpoint-xl) {
    gap: 85px;
    min-height: 450px;
  }

  /* Large desktop styles (1200px+) */
  @media (min-width: breakpoint-xl) {
    gap: 103px;
    min-height: 512px;
  }
}

.inner_container_resources {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 34px;

  /* Tablet styles (768px - 1024px) */
  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1024) {
    gap: 28px;
  }

  /* Desktop styles (1024px - 1200px) */
  @media (min-width: breakpoint-xl-1024) and (max-width: breakpoint-xl) {
    gap: 30px;
  }

  /* Large desktop styles (1200px+) */
  @media (min-width: breakpoint-xl) {
    gap: 34px;
  }
}

.section_top {
  display: flex;
  flex-direction: column;
  gap: 30px;
  max-width: 737px;
  transform: translateY(10%);
  transition: transform 1s ease-in-out;
  opacity: 0;

  /* Tablet styles (768px - 1024px) */
  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1024) {
    gap: 24px;
    max-width: 600px;
  }

  /* Desktop styles (1024px - 1200px) */
  @media (min-width: breakpoint-xl-1024) and (max-width: breakpoint-xl) {
    gap: 26px;
    max-width: 650px;
  }

  /* Large desktop styles (1200px - 2000px) */
  @media (min-width: breakpoint-xl) and (max-width: breakpoint-xl-2000) {
    gap: 30px;
    max-width: 737px;
  }

  @media screen and (min-width: breakpoint-xl-2000) {
    max-width: 847px;
  }
}

.section_top_resources {
  display: flex;
  flex-direction: column;
  gap: 30px;
  transform: translateY(10%);
  transition: transform 1s ease-in-out;
  opacity: 0;

  /* Tablet styles (768px - 1024px) */
  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1024) {
    gap: 24px;
  }

  /* Desktop styles (1024px - 1200px) */
  @media (min-width: breakpoint-xl-1024) and (max-width: breakpoint-xl) {
    gap: 26px;
  }

  /* Large desktop styles (1200px+) */
  @media (min-width: breakpoint-xl) {
    gap: 30px;
  }
}

.section_top_resources:hover {
  .carousel_title_resources > h1 {
    cursor: pointer;
    background: linear-gradient(
      to right,
      brandColorOne,
      brandColorTwo,
      brandColorThree,
      brandColorFour,
      brandColorFive
    );
    background-clip: text;
    color: transparent;
  }
}

.section_top_active {
  transform: translateY(0);
  animation: fadeIn 1s forwards;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 0.5;
  }

  100% {
    opacity: 1;
  }
}

.section_top_mobile {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
  padding: 16px;
}

.carousel_title > h1 {
  color: colorWhite;

  font-size: 48px;
  font-style: normal;
  font-weight: 700;
  line-height: 62px;

  /* Tablet styles (768px - 1024px) */
  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1024) {
    font-size: 36px;
    line-height: 46px;
  }

  /* Desktop styles (1024px - 1200px) */
  @media (min-width: breakpoint-xl-1024) and (max-width: breakpoint-xl) {
    font-size: 42px;
    line-height: 54px;
  }

  /* Large desktop styles (1200px+) */
  @media (min-width: breakpoint-xl) {
    font-size: 48px;
    line-height: 62px;
  }
}

.carousel_resources_link {
  text-decoration: none;
}

.carousel_title_resources > h1 {
  color: gray900;
  font-size: 48px;
  font-style: normal;
  font-weight: 700;
  line-height: 62px;
  transition: all 0.3s ease;

  /* Tablet styles (768px - 1024px) */
  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1024) {
    font-size: 36px;
    line-height: 46px;
  }

  /* Desktop styles (1024px - 1200px) */
  @media (min-width: breakpoint-xl-1024) and (max-width: breakpoint-xl) {
    font-size: 42px;
    line-height: 54px;
  }

  /* Large desktop styles (1200px+) */
  @media (min-width: breakpoint-xl) {
    font-size: 48px;
    line-height: 62px;
  }
}

.carousel_title_resources_mobile > h1 {
  color: #383838;

  text-align: center;
  font-size: 28px;
  font-style: normal;
  font-weight: 600;
  line-height: 38px;
  letter-spacing: -0.84px;
}

.carousel_title_mobile > h1 {
  color: colorWhite;

  text-align: center;
  font-size: 28px;
  font-style: normal;
  font-weight: 600;
  line-height: 38px;
  letter-spacing: -0.84px;
}

.homepage__desc {
  color: colorWhite;

  font-size: 22px;
  font-style: normal;
  font-weight: 400;
  line-height: 30px;

  /* Tablet styles (768px - 1024px) */
  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1024) {
    font-size: 18px;
    line-height: 26px;
  }

  /* Desktop styles (1024px - 1200px) */
  @media (min-width: breakpoint-xl-1024) and (max-width: breakpoint-xl) {
    font-size: 20px;
    line-height: 28px;
  }

  /* Large desktop styles (1200px+) */
  @media (min-width: breakpoint-xl) {
    font-size: 22px;
    line-height: 30px;
  }
}

.homepage__desc_resources {
  color: #383838;

  font-size: 22px;
  font-style: normal;
  font-weight: 400;
  line-height: 30px;

  /* Tablet styles (768px - 1024px) */
  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1024) {
    font-size: 18px;
    line-height: 26px;
  }

  /* Desktop styles (1024px - 1200px) */
  @media (min-width: breakpoint-xl-1024) and (max-width: breakpoint-xl) {
    font-size: 20px;
    line-height: 28px;
  }

  /* Large desktop styles (1200px+) */
  @media (min-width: breakpoint-xl) {
    font-size: 22px;
    line-height: 30px;
  }
}

.homepage__desc_resources > p {
  margin: 0;
}

.homepage__desc_mobile {
  color: colorWhite;

  text-align: center;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 23px;
}

.homepage__desc_mobile_resources {
  color: #383838;

  text-align: center;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 23px;
}

.homepage__desc_mobile_resources > p {
  margin: 0;
}

.circular_button_mobile {
  display: flex;
  justify-content: center;
}

.bottom_section_controls {
  display: flex;
  flex-direction: column;
  gap: 18px;
  max-width: 540px;
  z-index: 3;

  /* Tablet styles (768px - 1024px) */
  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1024) {
    gap: 16px;
    max-width: 450px;
  }

  /* Desktop styles (1024px - 1200px) */
  @media (min-width: breakpoint-xl-1024) and (max-width: breakpoint-xl) {
    gap: 17px;
    max-width: 500px;
  }

  /* Large desktop styles (1200px+) */
  @media (min-width: breakpoint-xl) {
    gap: 18px;
    max-width: 540px;
  }
}

.bottom_section_controls_mobile {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 18px;
}

.service_title {
  color: colorWhite;

  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.service_title_resources {
  color: #383838;

  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.service_title_resources_mobile {
  color: #383838;

  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.embla__controls {
  display: flex;
}

.embla__dots {
  display: flex;
  gap: 5px;
}

.embla__dot {
  width: 100px;
  height: 4px;
  margin: 10px 0;
  border-radius: 10px;
  background-color: #6f6f6f;

  /* Tablet styles (768px - 1024px) */
  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1024) {
    width: 80px;
    height: 3px;
    margin: 8px 0;
  }

  /* Desktop styles (1024px - 1200px) */
  @media (min-width: breakpoint-xl-1024) and (max-width: breakpoint-xl) {
    width: 90px;
    height: 4px;
    margin: 9px 0;
  }

  /* Large desktop styles (1200px+) */
  @media (min-width: breakpoint-xl) {
    width: 100px;
    height: 4px;
    margin: 10px 0;
  }
}

.embla__dot_mobile {
  width: 50px;
  height: 4px;
  border-radius: 10px;
  background-color: #6f6f6f;
}

.embla__dot_selected {
  width: 100px;
  height: 4px;
  border-radius: 10px;
  position: relative;

  /* Tablet styles (768px - 1024px) */
  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1024) {
    width: 80px;
    height: 3px;
  }

  /* Desktop styles (1024px - 1200px) */
  @media (min-width: breakpoint-xl-1024) and (max-width: breakpoint-xl) {
    width: 90px;
    height: 4px;
  }

  /* Large desktop styles (1200px+) */
  @media (min-width: breakpoint-xl) {
    width: 100px;
    height: 4px;
  }
}

.embla__dot_selected_mobile {
  width: 50px;
  height: 4px;
  border-radius: 10px;
  position: relative;
}

.embla__dot_selected_mobile::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  border-radius: 10px;
  background: linear-gradient(
    to right,
    #febe10,
    #f47a37,
    #f05443,
    #d91a5f,
    #b41f5e
  );
  animation: progress 6s linear forwards;
}

.embla__dot_selected::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  border-radius: 10px;
  background: linear-gradient(
    to right,
    #febe10,
    #f47a37,
    #f05443,
    #d91a5f,
    #b41f5e
  );
  animation: progress 6s linear forwards;
}

@keyframes progress {
  100% {
    width: 100%;
  }
}

.circular_button {
  width: fit-content;
}
