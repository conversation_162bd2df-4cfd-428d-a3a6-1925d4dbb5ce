'use client';

import React, { useEffect, useRef, useState } from 'react';
import style from './blogbody.module.css';
import { Container, Col, Row } from 'react-bootstrap';
import AboutAuthor from '@components/BlogAboutAuthor';
import SocialMediaIcons from '@components/BlogSocialMediaIcons';
import TableOfContent from '@components/BlogTableOfContent';
import { usePathname } from 'next/navigation';
import Button from '@components/Button';
import classNames from '@utils/classNames';
import Link from 'next/link';
import useForm from '@hooks/useForm';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import getUserLocation from '@utils/getUserLocation';

function BlogBody({
  blogData,
  setTocVisible,
  isTocVisible,
  isThereAnyTitleToShowToc,
  source = 'HomePage',
}) {
  const [visibleSection, setVisibleSection] = useState(null);
  const [checkBlog, setCheckBlog] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const userCountryCode = getUserLocation();

  const pathname = usePathname();

  useEffect(() => {
    setCheckBlog(pathname.split('#')[0]);
  }, [pathname]);

  const headerRef = useRef(null);
  const refs = blogData.content.map((_, index) => {
    const ref = React.createRef();
    return ref;
  });

  // Getting the current blog URL for social sharing buttons
  const blogurl = typeof window !== 'undefined' ? window.location.href : '';

  const scrollToElement = id => {
    const current = refs[id];
    current.current.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    });
  };

  const RenderHTML = ({ HTML }) => (
    <div
      className={`${style.blogbody__para} overflow-hidden`}
      dangerouslySetInnerHTML={{ __html: HTML }}
    ></div>
  );

  const getDimensions = ele => {
    const { height } = ele.getBoundingClientRect();
    const offsetTop = ele.offsetTop;
    const offsetBottom = offsetTop + height;

    return {
      height,
      offsetTop,
      offsetBottom,
    };
  };

  const sectionRefs = blogData.content.map((value, index) => ({
    section: value.title,
    ref: refs[index],
    id: value.id,
  }));

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = typeof window !== 'undefined' ? window.scrollY : 0;
      const selected = sectionRefs.find(({ ref }) => {
        const ele = ref.current;
        if (ele) {
          const { offsetTop, offsetBottom } = getDimensions(ele);
          return (
            scrollPosition + 70 >= offsetTop &&
            scrollPosition - 50 <= offsetBottom
          );
        }
      });

      if (selected && selected.section !== visibleSection) {
        setVisibleSection(selected.id);
      }
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [sectionRefs, visibleSection]);

  const shareOnTwitter = tweetlink => {
    const twitterShare = `https://twitter.com/intent/tweet?text=${tweetlink}`;
    if (typeof window !== 'undefined') {
      window.open(
        twitterShare,
        '_blank',
        'width=800,height=450,toolbar=no,menubar=no,resizable=yes left=400px top=200px',
      );
    }
  };

  const { values, errors, errorMessages, handleChange, handleSubmit } = useForm(
    {
      firstName: '',
      emailAddress: '',
      phoneNumber: '',
      companyName: '',
      consent: false,
    },
    {
      firstName: { empty: false },
      emailAddress: { empty: false, invalid: false },
      phoneNumber: { empty: false, invalid: false },
      companyName: { empty: false },
      consent: { empty: false },
    },
    'caseStudy',
    source,
  );
  const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    setIsSubmitting(true);

    try {
      await handleSubmit(event);
    } catch (error) {
      console.error('Form submission failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };
  return (
    <>
      <Container fluid>
        <Row className={`${style.blogbody__row} gx-0`}>
          <Col md={0} lg={3} xl={3} className={style.blogbody__toc}>
            <TableOfContent
              blogDataContent={blogData?.content}
              onIndexClick={scrollToElement}
              isTocVisible={isTocVisible}
              setTocVisible={setTocVisible}
              isThereAnyTitleToShowToc={isThereAnyTitleToShowToc}
              activeDivOnScroll={visibleSection}
            />
          </Col>
          <Col md={12} lg={6} xl={7} className={style.blogbody__content}>
            <div ref={headerRef}></div>
            {blogData?.content?.map((data, index) => (
              <React.Fragment key={index}>
                <h2
                  ref={refs[index]}
                  title={data.title}
                  className={style.blogbody__content__h2}
                >
                  {data?.title}
                </h2>

                <RenderHTML HTML={data?.description} />

                {data?.twitter_link_text && (
                  <div className={style.tweet_body}>
                    <p
                      onClick={() => shareOnTwitter(data?.twitter_link)}
                      className={style.tweet}
                    >
                      {data?.twitter_link_text}
                    </p>
                    <button
                      className={style.submitButton}
                      onClick={() => shareOnTwitter(data?.twitter_link)}
                    >
                      Tweet
                    </button>
                  </div>
                )}
              </React.Fragment>
            ))}
            <AboutAuthor
              blogsAuthorData={blogData?.authors?.data[0]?.attributes}
            />
          </Col>
          <Col md={0} lg={3} xl={2} className={style.blogbody__socialicons}>
            <div className={style.socialIconsWrapper}>
              <div
                className={classNames(
                  style.services_box_container,
                  style.box_border_gradient,
                )}
              >
                <div className={style.service_title}>
                  Stuck with a Tech Hurdle?
                </div>
                <div className={style.service_description}>
                  We fix, build, and optimize. The first consultation is on us!
                </div>
                <form className={style.form} onSubmit={onSubmit}>
                  <label
                    className={
                      errors.firstName.empty
                        ? style.errorLabel
                        : style.formFields
                    }
                  >
                    Full Name*{' '}
                  </label>
                  <input
                    className={
                      errors.firstName.empty
                        ? `${style.errorInput} ${style.formInput} `
                        : `${style.formInput}`
                    }
                    placeholder="Full Name"
                    type="text"
                    value={values.firstName}
                    id="firstName"
                    name="firstName"
                    maxLength={50}
                    onChange={e => handleChange(e?.target)}
                    onBlur={e => handleChange(e?.target)}
                  />
                  <label
                    className={
                      errors.emailAddress.empty || errors.emailAddress.invalid
                        ? style.errorLabel
                        : style.formFields
                    }
                  >
                    Email*
                  </label>
                  <input
                    className={
                      errors.emailAddress.empty
                        ? `${style.errorInput} ${style.formInput}`
                        : `${style.formInput}`
                    }
                    type="text"
                    id="emailAddress"
                    name="emailAddress"
                    placeholder="Your Email ID"
                    value={values.emailAddress}
                    maxLength={50}
                    onChange={e => handleChange(e?.target)}
                    onBlur={e => handleChange(e?.target)}
                  />
                  <label
                    className={
                      errors.phoneNumber.empty || errors.phoneNumber.invalid
                        ? style.errorLabel
                        : style.formFields
                    }
                  >
                    Phone Number*
                  </label>

                  <PhoneInput
                    inputClass={
                      errors.phoneNumber.empty || errors.phoneNumber.invalid
                        ? `${style.errorInput} ${style.ph_number_countries_input_services_page}`
                        : style.ph_number_countries_input_services_page
                    }
                    buttonClass={
                      errors.phoneNumber.empty || errors.phoneNumber.invalid
                        ? `${style.errorInput} ${style.ph_number_countries_button_services_page}`
                        : style.ph_number_countries_button_services_page
                    }
                    dropdownClass={
                      style.ph_number_countries_dropdown_services_page
                    }
                    preferredCountries={[
                      'us',
                      'gb',
                      'sg',
                      'de',
                      'sa',
                      'in',
                      'nl',
                      'au',
                      'be',
                      'my',
                    ]}
                    country={userCountryCode || 'us'}
                    placeholder="Your Phone Number"
                    value={values.phoneNumber}
                    onChange={value =>
                      handleChange({ value, name: 'phoneNumber' })
                    }
                    onBlur={e => handleChange(e?.target)}
                  />
                  <label className={style.formFields}>Company Name</label>
                  <input
                    className={style.formInput}
                    placeholder="Company Name"
                    type="text"
                    id="companyName"
                    name="companyName"
                    value={values.companyName}
                    maxLength={50}
                    onChange={e => handleChange(e?.target)}
                  />
                  {isSubmitting ? (
                    <div className={style.container_spinner}>
                      <div className={style.spinner}></div>
                    </div>
                  ) : (
                    <Button
                      className={style.submitButton}
                      label={'Submit'}
                      type="submit"
                    />
                  )}
                  <div className={style.errorMessages}>
                    <div>{errorMessages.empty && errorMessages.empty}</div>
                    <div>{errorMessages.invalid && errorMessages.invalid}</div>
                  </div>
                </form>
              </div>
              <SocialMediaIcons variant="vertical" blogurl={blogurl} />
            </div>
          </Col>
        </Row>
      </Container>
    </>
  );
}

export default BlogBody;
