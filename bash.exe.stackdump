Stack trace:
Frame         Function      Args
0007FFFFBE20  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFBE20, 0007FFFFAD20) msys-2.0.dll+0x1FE8E
0007FFFFBE20  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC0F8) msys-2.0.dll+0x67F9
0007FFFFBE20  000210046832 (000210286019, 0007FFFFBCD8, 0007FFFFBE20, 000000000000) msys-2.0.dll+0x6832
0007FFFFBE20  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBE20  000210068E24 (0007FFFFBE30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC100  00021006A225 (0007FFFFBE30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCD8380000 ntdll.dll
7FFCD7970000 KERNEL32.DLL
7FFCD54D0000 KERNELBASE.dll
7FFCD80B0000 USER32.dll
7FFCD5A10000 win32u.dll
7FFCD66C0000 GDI32.dll
7FFCD5FE0000 gdi32full.dll
7FFCD5960000 msvcp_win.dll
7FFCD5D10000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFCD7FF0000 advapi32.dll
7FFCD63A0000 msvcrt.dll
7FFCD6610000 sechost.dll
7FFCD7B40000 RPCRT4.dll
7FFCD4BD0000 CRYPTBASE.DLL
7FFCD58C0000 bcryptPrimitives.dll
7FFCD6AC0000 IMM32.DLL
