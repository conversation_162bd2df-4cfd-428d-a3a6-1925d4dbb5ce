import React from 'react';
import seoSchema from '@utils/seoSchema';
import HeroSection from '@components/HeroSection';
import CTA from '@components/CTA';
import CaseStudyCard from '@components/CaseStudyCard';
import TechStack from '@components/TechStack';
import WhyChooseMTL from '@components/WhyChooseMTL';
import Challenges from '@components/Challenges';
import TitleDescription from '@components/TitleDescription';
import BusinessUseCases from '@components/BusinessUseCases';
import { notFound } from 'next/navigation';
import ContactUsForm from '@components/ContactUsForm';
import RichResults from '@components/RichResults';
import fetchFromStrapi from '@utils/fetchFromStrapi';

export async function generateStaticParams() {
  const solutionPageResponse = await fetchFromStrapi('solutions');
  const solutionSlug = solutionPageResponse.data.map(res => ({
    solutionSlug: res.attributes.slug,
  }));

  return solutionSlug;
}

export async function fetchSolutionData(slug: string) {
  const queryString = `filters[slug][$eq]=${slug}&populate=hero_section.image,hero_section.mobile_image,solution,cta,case_study_cards.case_study_relation.preview.preview_background_image&populate=case_study_cards.case_study_relation.hero_section.global_services,tech_stack.tab.logo_url,why_choose_mtl.whyChooseMtlCards,challenges.challenges_box.image,business_use_cases.challenges_box.image,seo.schema`;
  return await fetchFromStrapi('solutions', queryString);
}

async function getFormData() {
  const queryString = 'populate=form.formFields&populate=form.button';
  return await fetchFromStrapi('form', queryString);
}

export async function generateMetadata({
  params,
}: {
  params: { solutionSlug: string };
}) {
  const { solutionSlug: slug } = params;
  const queryString = `filters[slug][$eq]=${slug}&populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords,seo.schema`;
  const seoFetchedData = await fetchFromStrapi('solutions', queryString);
  const seoData = seoFetchedData?.data[0]?.attributes?.seo;

  return seoSchema(seoData);
}

export default async function Partners({ params }: { params: any }) {
  const { solutionSlug } = params;

  const solutionData = await fetchSolutionData(solutionSlug);
  const formData = await getFormData();

  // Check if solution page data exists, otherwise return 404
  if (!solutionData?.data || solutionData?.data.length === 0) {
    notFound();
  }

  return (
    <>
      {solutionData?.data[0]?.attributes?.seo && (
        <RichResults data={solutionData?.data[0]?.attributes?.seo} />
      )}
      {solutionData?.data[0]?.attributes?.hero_section && (
        <HeroSection
          heroData={solutionData?.data[0]?.attributes?.hero_section}
          variant="primary"
        />
      )}
      {solutionData?.data[0]?.attributes?.challenges && (
        <Challenges
          dataChallenges={solutionData?.data[0]?.attributes?.challenges}
          variantWhite={true}
        />
      )}
      {solutionData?.data[0]?.attributes?.solution && (
        <TitleDescription
          dataTitleDescription={solutionData?.data[0]?.attributes?.solution}
        />
      )}
      {solutionData?.data[0]?.attributes?.cta && (
        <CTA
          data={solutionData?.data[0]?.attributes?.cta}
          variant="scrollToContactForm"
        />
      )}

      {solutionData?.data[0]?.attributes?.business_use_cases && (
        <BusinessUseCases
          dataBusinessUseCases={
            solutionData?.data[0]?.attributes?.business_use_cases
          }
        />
      )}

      {solutionData?.data[0]?.attributes?.case_study_cards && (
        <CaseStudyCard
          case_study={solutionData?.data[0]?.attributes?.case_study_cards}
        />
      )}
      {solutionData?.data[0]?.attributes?.tech_stack && (
        <TechStack data={solutionData?.data[0]?.attributes?.tech_stack} />
      )}

      {solutionData?.data[0]?.attributes?.why_choose_mtl && (
        <WhyChooseMTL
          data={solutionData?.data[0]?.attributes?.why_choose_mtl}
        />
      )}
      {formData?.data?.attributes?.form && (
        <ContactUsForm
          formData={formData?.data?.attributes?.form}
          source="Solutions"
        />
      )}
    </>
  );
}
